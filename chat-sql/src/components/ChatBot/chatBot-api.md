#### 流式输出

```apl
// 流式输出：
const axios = require('axios');

async function callDashScope() {
    //若没有配置环境变量，可用百炼API Key将下行替换为：apiKey='sk-xxx'。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
    const apiKey = process.env.DASHSCOPE_API_KEY;
    const appId = 'YOUR_APP_ID';// 替换为实际的应用 ID

    const url = `https://dashscope.aliyuncs.com/api/v1/apps/${appId}/completion`;

    const data = {
        input: {
            prompt: "你是谁？"
        },
        parameters: {
            'incremental_output' : 'true' // 增量输出
        },
        debug: {}
    };

    try {
        console.log("Sending request to DashScope API...");

        const response = await axios.post(url, data, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'X-DashScope-SSE': 'enable' // 流式输出
            },
            responseType: 'stream' // 用于处理流式响应
        });

        if (response.status === 200) {
            // 处理流式响应
            response.data.on('data', (chunk) => {
                console.log(`Received chunk: ${chunk.toString()}`);
            });
        } else {
            console.log("Request failed:");
            if (response.data.request_id) {
                console.log(`request_id=${response.data.request_id}`);
            }
            console.log(`code=${response.status}`);
            if (response.data.message) {
                console.log(`message=${response.data.message}`);
            } else {
                console.log('message=Unknown error');
            }
        }
    } catch (error) {
        console.error(`Error calling DashScope: ${error.message}`);
        if (error.response) {
            console.error(`Response status: ${error.response.status}`);
            console.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
    }
}

callDashScope();
```



#### 多轮对话

通过云端托管的方式：通过传入 session_id，系统会自动从云端加载存储的对话历史，并结合新的指令生成上下文。
但是继续维护本地的indexDB中的消息数组，用于窗口中的聊天记录展示

必传参数：session_id 和 prompt

参考实现：

```apl
const axios = require('axios');

async function callDashScope() {
    // 若没有配置环境变量，可用百炼API Key将下行替换为：apiKey='sk-xxx'。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
    const apiKey = process.env.DASHSCOPE_API_KEY;
    const appId = 'YOUR_APP_ID';// 替换为实际的应用 ID

    const url = `https://dashscope.aliyuncs.com/api/v1/apps/${appId}/completion`;

    const data = {
        input: {
            prompt: "你是谁？"
        },
        parameters: {},
        debug: {}
    };

    try {
        const response = await axios.post(url, data, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 200) {
            console.log(`${response.data.output.text}`);
            console.log(`session_id=${response.data.output.session_id}`);
        } else {
            console.log(`request_id=${response.headers['request_id']}`);
            console.log(`code=${response.status}`);
            console.log(`message=${response.data.message}`);
        }
    } catch (error) {
        console.error(`Error calling DashScope: ${error.message}`);
        if (error.response) {
            console.error(`Response status: ${error.response.status}`);
            console.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
    }
}
callDashScope();

// 下一轮
const axios = require('axios');

async function callDashScope() {
    // 若没有配置环境变量，可用百炼API Key将下行替换为：apiKey='sk-xxx'。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
    const apiKey = process.env.DASHSCOPE_API_KEY;
    const appId = 'YOUR_APP_ID';// 替换为实际的应用 ID

    const url = `https://dashscope.aliyuncs.com/api/v1/apps/${appId}/completion`;
    // session_id替换为实际上一轮对话的session_id
    const data = {
        input: {
            prompt: "你有什么技能？",
            session_id: 'fe4ce8b093bf46159ea9927a7b22f0d3',
        },
        parameters: {},
        debug: {}
    };

    try {
        const response = await axios.post(url, data, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 200) {
            console.log(`${response.data.output.text}`);
            console.log(`session_id=${response.data.output.session_id}`);
        } else {
            console.log(`request_id=${response.headers['request_id']}`);
            console.log(`code=${response.status}`);
            console.log(`message=${response.data.message}`);
        }
    } catch (error) {
        console.error(`Error calling DashScope: ${error.message}`);
        if (error.response) {
            console.error(`Response status: ${error.response.status}`);
            console.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
    }
}
callDashScope();

```

因此，  我觉得本地对于每个对话需要维护一个session_id, 初始化为空；一旦开始聊天，智能体就会返回对应的session_id，我们将其存储，然后使用session_id作为数组实现多轮对话



#### 传递自定义参数

通过biz_params传递所需参数以实现灵活适配

我们一般使用其中的“提示词变量”，用 `user_prompt_params`输入， 比如：

```apl
const axios = require('axios');

async function callDashScope() {
    // 若没有配置环境变量，可用百炼API Key将下行替换为：apiKey='sk-xxx'。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
    const apiKey = process.env.DASHSCOPE_API_KEY;
    const appId = 'YOUR_APP_ID';// 替换为实际的应用 ID
    const url = `https://dashscope.aliyuncs.com/api/v1/apps/${appId}/completion`;

    // user_prompt_params可传递多个自定义变量键值对，英文逗号隔开。
    const data = {
        input: {
            prompt: "美食推荐",
            biz_params: {
                user_prompt_params: {      
                    'city': '北京'  
                }
            }
        },
        parameters: {},
        debug: {}
    };

    try {
        console.log("Sending request to DashScope API...");

        const response = await axios.post(url, data, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 200) {
            if (response.data.output && response.data.output.text) {
                console.log(`${response.data.output.text}`);
            }
        } else {
            console.log("Request failed:");
            if (response.data.request_id) {
                console.log(`request_id=${response.data.request_id}`);
            }
            console.log(`code=${response.status}`);
            if (response.data.message) {
                console.log(`message=${response.data.message}`);
            } else {
                console.log('message=Unknown error');
            }
        }
    } catch (error) {
        console.error(`Error calling DashScope: ${error.message}`);
        if (error.response) {
            console.error(`Response status: ${error.response.status}`);
            console.error(`Response data: ${JSON.stringify(error.response.data, null, 2)}`);
        }
    }
}
callDashScope();
```



#### 具体的api

```apl
YOUR_APP_ID替换为实际的应用 ID。下一轮对话的输入参数session_id字段值替换为实际上一轮对话返回的session_id值。
app_id string （必选）

应用的标识。

在应用管理页面的应用卡片上可以获取应用ID。

应用ID无法通过API获取。
Java SDK中为appId。通过HTTP调用时，请将实际的应用ID放入 URL 中，替换YOUR_APP_ID。
prompt string （必选）

输入当前期望应用执行的指令prompt，用来指导应用生成回复。

暂不支持传入文件。如果应用使用的是Qwen-Long模型，应用调用方法与其他模型一致。

当您通过传入messages自己管理对话历史时，则无需传递prompt。

通过HTTP调用时，请将 prompt 放入 input 对象中。
session_id string （可选）

历史对话的唯一标识。

传入session_id时，请求将自动携带云端存储的对话历史。具体用法请参阅多轮对话。

传入session_id时，prompt为必传。
若同时传入session_id和messages，则优先使用传入的messages。
目前仅智能体应用和对话型工作流应用支持多轮对话。
Java SDK中为setSessionId。通过HTTP调用时，请将 session_id 放入 input 对象中。
messages array （可选）
```

> 注意我们传入的是session_id



### 其他信息

#### 字段

```apl
// 智能体的回复
"text": ... ,
"metadata":{
    "module": 用于页面的跳转
    "topic": 用于细分模块下的具体任务
}
```

```apl
// 智能体对话的历史记录
interface ChatHistory {
  id: string;
  timestamp: string;
  messages: Message[];
  module: 'coding' | 'ER' | 'Bplus';
  action?: { type: string; target: string; params?: Record<string, string> };
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
}

```

#### 功能抽象

// 对话框的功能
1. 历史记录
2. 允许跳转的‘tag’ / 按钮形式
3. 允许在对话的“设置”的modal中的一个tab管理：
    1. 始终作为上下文的“系统提示词”；
    2. 允许用户设置自己的api-key与平台（优先实现百炼平台的， 后续考虑扩展到dify）



#### 设计整体思路

```apl
// 设计思路
1. useChat hook 来管理对话状态、发送消息和处理响应，一个 useChatHistory hook 来管理历史记录的存取；
2. 将自己的api-key存储在.env文件中，避免泄露 ； 但是我们可能需要在官网提供自己的api-key作为临时的访问方式？
3. 整体使用 React + MUI + TypeScript 实现悬浮式聊天窗口
4. 使用 React Router 的 useNavigate hook 解析 action 字段，跳转到指定页面，并通过 Context 或 props 更新可视化内容。 用户需要点击按钮确认跳转， 而不是直接跳转
5. 整体的context需要分别存储用户在不同模块中选中的会话记录，避免刷新/跳转时无法回到之前选中的会话。
6. 以ER为例，智能体给出对应的json字段可以被可视化组件渲染的时候， 用户应当可以选择是直接替换当前的会话记录， 还是新建一个默认的记录
7. 快捷键：Ctrl + K：打开/关闭聊天窗口； Ctrl + H：打开历史记录面板。我希望通过快捷键打开的历史记录面板是独立的，也就是一个modal，可以用上下箭头导航，空格/回车打开对应的聊天窗口；然后使用快捷键打开聊天窗口之后，左侧暂时有3个sidebar，分别是新建记录、打开侧边栏（历史记录）以及下方的“设置”按钮，样式可以参考当前页面的sidebar的风格，“设置”点击之后弹出modal来进行设置...
8. 使用node.js，axios搭建前端与智能体之间的中间人
```

#### api-key

```apl
api-key： sk-029b12329b3349b1bab58c522a07a2c8
对应的应用id： 6533b3711b8143068af6b09b98a3323c
```

需要设置.env获取

